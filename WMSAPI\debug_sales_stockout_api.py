#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试销售出库单查询接口
"""

import logging
from datetime import datetime, timedelta
from wdt_post_client import WDTPostClient
import json

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def debug_sales_stockout_api():
    """调试销售出库单查询接口"""
    logger = setup_logging()
    client = WDTPostClient()
    
    logger.info("=== 调试销售出库单查询接口 ===")
    
    # 获取当天时间范围
    now = datetime.now()
    start_time = datetime.combine(now.date(), datetime.min.time())
    end_time = now
    
    start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    logger.info(f"查询时间范围: {start_str} 至 {end_str}")
    
    # 测试不同的销售出库单查询方式
    test_cases = [
        {
            "name": "销售出库单查询 - 基础查询",
            "method": "sales.stockout.query",
            "params": {
                "start_time": start_str,
                "end_time": end_str,
                "page_size": 20,
                "page_no": 0
            }
        },
        {
            "name": "销售出库单查询 - 扩大时间范围",
            "method": "sales.stockout.query", 
            "params": {
                "start_time": (now - timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S'),
                "end_time": end_str,
                "page_size": 20,
                "page_no": 0
            }
        },
        {
            "name": "销售出库单查询 - 使用修改时间",
            "method": "sales.stockout.query",
            "params": {
                "start_modified": start_str,
                "end_modified": end_str,
                "page_size": 20,
                "page_no": 0
            }
        },
        {
            "name": "出库单查询 - 使用时间范围",
            "method": "stockout.query",
            "params": {
                "start_consign_time": start_str,
                "end_consign_time": end_str,
                "page_size": 20,
                "page_no": 0
            }
        },
        {
            "name": "出库单查询 - 使用创建时间",
            "method": "stockout.query",
            "params": {
                "start_time": start_str,
                "end_time": end_str,
                "page_size": 20,
                "page_no": 0
            }
        }
    ]
    
    for test_case in test_cases:
        logger.info(f"\n--- {test_case['name']} ---")
        logger.info(f"API方法: {test_case['method']}")
        logger.info(f"查询参数: {json.dumps(test_case['params'], ensure_ascii=False, indent=2)}")
        
        try:
            result = client.call_api(test_case['method'], test_case['params'])
            
            if result:
                if result.get('flag') == 'success':
                    content = result.get('content', [])
                    logger.info(f"✅ 查询成功，返回 {len(content)} 条记录")
                    
                    if content:
                        # 分析物流单号分布
                        logistics_count = {}
                        jd_logistics = []
                        
                        for i, item in enumerate(content[:5]):  # 只分析前5条
                            logger.info(f"\n  记录 {i+1}:")
                            logger.info(f"    出库单号: {item.get('stockout_no', '未知')}")
                            logger.info(f"    订单号: {item.get('trade_no', '未知')}")
                            logger.info(f"    物流单号: {item.get('logistics_no', '未知')}")
                            logger.info(f"    物流编码: {item.get('logistics_code', '未知')}")
                            logger.info(f"    状态: {item.get('status', '未知')}")
                            logger.info(f"    创建时间: {item.get('created', '未知')}")
                            logger.info(f"    发货时间: {item.get('consign_time', '未知')}")
                            
                            # 统计物流编码
                            logistics_code = item.get('logistics_code', '')
                            if logistics_code:
                                logistics_count[logistics_code] = logistics_count.get(logistics_code, 0) + 1
                            
                            # 收集京东物流单号
                            if logistics_code and logistics_code.upper() in ['JBD', 'JD', 'JINGDONG']:
                                logistics_no = item.get('logistics_no', '')
                                if logistics_no:
                                    jd_logistics.append(logistics_no)
                        
                        # 显示统计信息
                        logger.info(f"\n  📊 物流编码统计:")
                        for code, count in logistics_count.items():
                            logger.info(f"    {code}: {count}个")
                        
                        if jd_logistics:
                            logger.info(f"\n  🚚 京东物流单号:")
                            for jd_no in jd_logistics:
                                logger.info(f"    {jd_no}")
                        else:
                            logger.info(f"\n  ⚠️ 未找到京东物流单号")
                    
                    logger.info(f"\n  总记录数: {result.get('total', '未知')}")
                else:
                    logger.error(f"❌ 查询失败: {result.get('message', '未知错误')}")
            else:
                logger.error("❌ API调用返回空结果")
                
        except Exception as e:
            logger.error(f"❌ 查询异常: {e}")

def test_specific_trade_nos():
    """测试特定订单号的出库单查询"""
    logger = setup_logging()
    client = WDTPostClient()
    
    logger.info("\n=== 测试特定订单号的出库单查询 ===")
    
    # 使用已知的订单号
    test_trade_nos = [
        "LL202507161950",
        "LL202507170012", 
        "LL202507170035",
        "LL202507170133",
        "LL202507170056"
    ]
    
    for trade_no in test_trade_nos:
        logger.info(f"\n--- 查询订单号: {trade_no} ---")
        
        # 尝试不同的查询方式
        test_methods = [
            {
                "name": "销售出库单查询",
                "method": "sales.stockout.query",
                "params": {"trade_no": trade_no, "page_size": 10, "page_no": 0}
            },
            {
                "name": "出库单查询 - 使用src_tid",
                "method": "stockout.query", 
                "params": {"src_tid": trade_no, "page_size": 10, "page_no": 0}
            }
        ]
        
        for test_method in test_methods:
            logger.info(f"\n  {test_method['name']}:")
            try:
                result = client.call_api(test_method['method'], test_method['params'])
                
                if result and result.get('flag') == 'success':
                    content = result.get('content', [])
                    logger.info(f"    ✅ 查询成功，返回 {len(content)} 条记录")
                    
                    for item in content:
                        logistics_no = item.get('logistics_no', '')
                        logistics_code = item.get('logistics_code', '')
                        status = item.get('status', '')
                        
                        logger.info(f"      出库单号: {item.get('stockout_no', '未知')}")
                        logger.info(f"      物流单号: {logistics_no}")
                        logger.info(f"      物流编码: {logistics_code}")
                        logger.info(f"      状态: {status}")
                        
                        if logistics_code and logistics_code.upper() in ['JBD', 'JD', 'JINGDONG']:
                            logger.info(f"      🎯 这是京东物流！")
                else:
                    logger.info(f"    ❌ 查询失败: {result.get('message', '未知错误') if result else '无响应'}")
                    
            except Exception as e:
                logger.info(f"    ❌ 查询异常: {e}")

if __name__ == '__main__':
    print("🔍 开始调试销售出库单查询接口...")
    print("\n请选择调试模式:")
    print("1. 调试销售出库单查询接口")
    print("2. 测试特定订单号查询")
    print("3. 全部测试")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        debug_sales_stockout_api()
    elif choice == "2":
        test_specific_trade_nos()
    elif choice == "3":
        debug_sales_stockout_api()
        test_specific_trade_nos()
    else:
        print("无效选择，退出程序")
    
    print("\n🎯 调试完成")
