@echo off
chcp 65001 >nul
echo 🏗️ 旺店通数据自动获取工具打包脚本
echo ============================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 检查pip是否可用
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: pip不可用
    pause
    exit /b 1
)

echo ✅ pip工具检查通过
echo.

REM 安装依赖
echo 📦 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装完成
echo.

REM 运行打包脚本
echo 🔨 开始打包程序...
python build.py
if errorlevel 1 (
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo 🎉 打包完成！
echo 📁 输出文件在 dist 目录中
echo.

REM 询问是否打开输出目录
set /p choice="是否打开输出目录？(y/n): "
if /i "%choice%"=="y" (
    explorer dist
)

pause
