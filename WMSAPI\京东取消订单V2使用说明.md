# 京东取消订单微信通知工具 V2 使用说明

## 🆕 V2版本新功能

### 1. 本地微信发送支持
- ✅ **直接操作本地微信窗口发送消息**
- ✅ **无需配置企业微信机器人**
- ✅ **支持发送给任何微信联系人或群**
- ✅ **自动查找和激活微信窗口**

### 2. 优化的出库明细查询
- ✅ **获取当天所有销售出库明细**
- ✅ **智能筛选已取消状态的记录**
- ✅ **提取JD开头的物流单号**
- ✅ **详细的统计报告**

## 🚀 快速开始

### 方式一：使用新版管理器（推荐）
```bash
python jd_cancel_manager_v2.py
```

### 方式二：使用GUI界面
```bash
python jd_cancel_gui.py
```

## 📱 微信发送方式对比

| 特性 | 本地微信发送 | 企业微信机器人 |
|------|-------------|---------------|
| 配置难度 | ⭐ 简单 | ⭐⭐⭐ 复杂 |
| 发送对象 | 任何联系人/群 | 仅企业微信群 |
| 网络要求 | 无特殊要求 | 需要外网访问 |
| 稳定性 | ⭐⭐⭐⭐ 高 | ⭐⭐⭐ 中等 |
| 推荐度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🔧 本地微信发送配置

### 前提条件
1. **微信PC版已安装并登录**
2. **Python环境已安装所需依赖**
   ```bash
   pip install pyautogui pyperclip pywin32
   ```

### 配置步骤
1. **启动程序**
2. **选择"本地微信发送"**
3. **输入联系人名称**
   - 可以是好友昵称
   - 可以是群名称
   - 建议使用"文件传输助手"进行测试

### 注意事项
- 确保微信窗口可见且未被其他窗口遮挡
- 联系人名称必须完全匹配
- 首次使用建议先测试连接

## 📊 功能特性

### 1. 智能出库明细查询
```
📦 查询流程:
获取当天所有出库明细 → 筛选已取消记录 → 提取京东物流单号 → 发送微信通知
```

### 2. 详细统计报告
- 总出库单数量
- 已取消出库单数量
- 京东物流单号数量
- 各状态出库单统计
- 物流公司分布统计

### 3. 微信消息格式
```
物流单号
JDAZ20474031170
JDAZ20473059809
JDAZ20472646572

以上京东单号订单取消，实物未发出共计3单，请处理！
```

## 🖥️ GUI界面新功能

### 微信发送方式选择
- **本地微信**: 直接操作本地微信发送
- **企业微信机器人**: 通过API发送

### 配置项说明
| 配置项 | 本地微信模式 | 企业微信机器人模式 |
|--------|-------------|------------------|
| 联系人/群名称 | ✅ 必填 | ✅ 必填 |
| Webhook URL | ❌ 不需要 | ✅ 必填 |

## 📋 使用示例

### 示例1: 本地微信发送
```python
from jd_cancel_manager_v2 import JDCancelManagerV2

# 创建管理器
manager = JDCancelManagerV2(
    wechat_contact="文件传输助手",
    use_local_wechat=True
)

# 处理当天的京东取消订单
result = manager.process_today_jd_cancellations()

# 打印报告
manager.print_detailed_report(result)
```

### 示例2: 企业微信机器人发送
```python
manager = JDCancelManagerV2(
    wechat_contact="@all",
    use_local_wechat=False,
    webhook_url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx"
)
```

## 🔍 测试功能

### 1. 微信连接测试
- 发送测试消息验证连接
- 确认联系人名称正确
- 验证微信窗口可操作

### 2. 出库明细查询测试
- 查询当天所有出库明细
- 显示详细统计信息
- 验证API权限正常

## 📈 运行结果示例

```
============================================================
📊 京东取消订单处理报告
============================================================
📅 处理时间: 2025-07-16 15:13:33
📦 总出库单数: 30
❌ 已取消出库单数: 0
🚚 京东物流单号数: 0
📱 微信发送方式: 本地微信
✅ 通知发送状态: 失败

✅ 今天没有已取消的京东订单

📈 出库单状态统计:
  已发货: 30个
```

## ⚠️ 注意事项

### 本地微信发送
1. **微信窗口状态**
   - 确保微信已登录
   - 微信窗口不能最小化
   - 避免其他程序遮挡微信窗口

2. **联系人名称**
   - 必须与微信中显示的名称完全一致
   - 区分大小写
   - 建议先用"文件传输助手"测试

3. **系统权限**
   - 程序需要控制鼠标和键盘的权限
   - 某些安全软件可能会拦截

### 企业微信机器人
1. **网络连接**
   - 需要能访问企业微信API
   - 确保webhook地址正确

2. **机器人权限**
   - 确保机器人在目标群中
   - 机器人未被禁用

## 🔧 故障排除

### 问题1: 找不到微信窗口
**解决方法:**
- 确保微信PC版已启动
- 检查微信是否已登录
- 重启微信程序

### 问题2: 联系人搜索失败
**解决方法:**
- 检查联系人名称是否正确
- 尝试使用"文件传输助手"
- 确认联系人在微信中可见

### 问题3: 消息发送失败
**解决方法:**
- 检查微信窗口是否被遮挡
- 确认有足够的系统权限
- 尝试手动发送一条消息测试

## 📦 依赖库安装

```bash
# 安装所有依赖
pip install -r requirements.txt

# 或单独安装本地微信发送依赖
pip install pyautogui pyperclip pywin32
```

## 🎯 最佳实践

1. **首次使用**
   - 先用"文件传输助手"测试
   - 确认所有功能正常后再配置实际联系人

2. **定时任务**
   - 建议使用GUI界面设置定时任务
   - 选择合适的执行频率

3. **监控运行**
   - 定期查看日志输出
   - 关注错误信息和异常

4. **备份配置**
   - 保存重要的配置信息
   - 定期备份配置文件

## 🆚 版本对比

| 功能 | V1版本 | V2版本 |
|------|--------|--------|
| 微信发送 | 仅企业微信机器人 | 本地微信 + 企业微信机器人 |
| 数据查询 | 销售订单API | 出库明细API |
| 物流单号获取 | 间接获取 | 直接获取 |
| 统计报告 | 基础统计 | 详细统计 |
| 用户体验 | 一般 | 优秀 |

V2版本在功能完整性、易用性和稳定性方面都有显著提升！
