# 旺店通数据自动获取工具打包说明

## 📦 打包概述

本项目提供了完整的打包解决方案，可以将Python程序打包成独立的Windows可执行文件，无需在目标机器上安装Python环境。

## 📁 打包相关文件

### 核心文件
1. **wdt_data_gui.spec** - PyInstaller配置文件
2. **requirements.txt** - 依赖包列表
3. **build.py** - 自动化打包脚本
4. **打包程序.bat** - 一键打包批处理文件

### 项目文件
- **wdt_data_gui.py** - 主GUI程序
- **export_to_wdt_data.py** - 数据导出模块
- **wdt_post_client.py** - API客户端
- **config.py** - 配置模块

## 🚀 快速打包

### 方法1: 一键打包（推荐）
```bash
# 双击运行
打包程序.bat
```

### 方法2: 手动打包
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行打包脚本
python build.py
```

### 方法3: 直接使用PyInstaller
```bash
# 安装PyInstaller
pip install pyinstaller

# 使用SPEC文件打包
pyinstaller wdt_data_gui.spec --clean --noconfirm
```

## 📋 依赖包说明

### 核心依赖
```
requests>=2.28.0          # HTTP请求库
openpyxl>=3.0.0          # Excel文件处理
python-dotenv>=0.19.0    # 环境变量管理
pyinstaller>=5.0.0       # 打包工具
```

### 标准库（无需安装）
- tkinter - GUI界面
- threading - 多线程
- time - 时间处理
- os - 操作系统接口
- json - JSON处理
- hashlib - 哈希算法
- logging - 日志记录
- datetime - 日期时间

## 🔧 SPEC文件配置

### 主要配置项
```python
# 主程序入口
['wdt_data_gui.py']

# 数据文件
datas=[
    ('config.py', '.'),
    ('wdt_gui_config.json', '.'),
    ('货品档案.xlsx', '.'),
    # 说明文档
]

# 隐藏导入
hiddenimports=[
    'tkinter', 'requests', 'openpyxl',
    'wdt_post_client', 'export_to_wdt_data'
]

# 可执行文件配置
name='旺店通数据自动获取工具'
console=False  # 不显示控制台
```

## 📊 打包过程

### 自动化流程
1. **检查依赖** - 验证所需包是否安装
2. **清理构建** - 删除旧的构建文件
3. **验证文件** - 检查项目文件完整性
4. **构建程序** - 使用PyInstaller打包
5. **创建发布包** - 生成完整的发布包

### 输出结构
```
dist/
├── 旺店通数据自动获取工具.exe          # 主程序
└── 旺店通数据自动获取工具_v20250714/   # 发布包
    ├── 旺店通数据自动获取工具.exe
    ├── 配置文件示例.json
    ├── 货品档案示例.xlsx
    ├── README.txt
    └── 使用说明/
        ├── GUI使用说明.md
        ├── 每日定时功能说明.md
        └── 其他说明文档...
```

## ⚠️ 注意事项

### 环境要求
- **Python版本**: 3.7+
- **操作系统**: Windows 10/11
- **内存**: 至少2GB可用内存
- **磁盘空间**: 至少500MB可用空间

### 常见问题

#### Q1: 打包失败，提示缺少模块
**A**: 检查requirements.txt中的依赖是否完整安装
```bash
pip install -r requirements.txt --upgrade
```

#### Q2: 可执行文件太大
**A**: 可以在SPEC文件中添加更多排除项
```python
excludes=[
    'matplotlib', 'numpy', 'pandas', 'scipy',
    'PIL', 'cv2', 'tensorflow', 'torch'
]
```

#### Q3: 运行时提示缺少文件
**A**: 检查SPEC文件中的datas配置，确保包含所有必要文件

#### Q4: 杀毒软件误报
**A**: 这是PyInstaller打包程序的常见问题，可以：
- 添加杀毒软件白名单
- 使用代码签名证书
- 联系杀毒软件厂商报告误报

### 优化建议

#### 减小文件大小
1. **排除不需要的模块**
```python
excludes=['matplotlib', 'numpy', 'pandas']
```

2. **使用UPX压缩**
```python
upx=True
```

3. **移除调试信息**
```python
debug=False
strip=True
```

#### 提高启动速度
1. **预编译Python字节码**
```python
optimize=2
```

2. **减少导入的模块**
3. **使用延迟导入**

## 📈 性能指标

### 典型构建结果
- **可执行文件大小**: 50-80MB
- **打包时间**: 2-5分钟
- **启动时间**: 3-8秒
- **内存占用**: 50-100MB

### 优化后结果
- **可执行文件大小**: 30-50MB
- **启动时间**: 2-5秒
- **内存占用**: 30-60MB

## 🎯 发布流程

### 1. 准备发布
```bash
# 更新版本信息
# 检查所有功能
# 运行测试
```

### 2. 执行打包
```bash
# 运行打包脚本
python build.py
```

### 3. 质量检查
```bash
# 测试可执行文件
# 检查文件完整性
# 验证功能正常
```

### 4. 创建发布
```bash
# 上传到发布平台
# 更新版本说明
# 通知用户更新
```

## 📞 技术支持

### 打包问题排查
1. **查看构建日志** - 检查详细错误信息
2. **验证依赖** - 确保所有依赖正确安装
3. **检查文件路径** - 确保所有文件路径正确
4. **测试环境** - 在干净环境中测试

### 联系方式
- 查看项目文档
- 提交Issue报告
- 联系技术支持

---

**文档版本**: v1.0  
**更新时间**: 2025-07-14  
**适用版本**: 旺店通数据自动获取工具 v2.2+
