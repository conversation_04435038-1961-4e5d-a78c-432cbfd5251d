@echo off
chcp 65001 >nul
echo 🚀 启动京东取消订单微信通知工具
echo ============================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 检查必要的库是否安装
echo 📦 检查依赖库...
python -c "import requests, json, tkinter, pyautogui, pyperclip" >nul 2>&1
if errorlevel 1 (
    echo 正在安装必要的依赖库...
    pip install requests python-dotenv pyautogui pyperclip pywin32
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖库检查通过
echo.

REM 启动GUI程序
echo 🎯 启动京东取消订单微信通知工具...
echo.
python jd_cancel_gui.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    pause
    exit /b 1
)

echo.
echo 👋 程序已退出
pause
