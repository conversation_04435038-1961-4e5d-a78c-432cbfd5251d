# 每日定时执行功能说明

## 🎯 新增功能概述

在原有的间隔执行基础上，新增了**每日定时执行**功能，用户可以设定每天的具体执行时间点，实现更精确的定时控制。

## 📋 功能特点

### 1. 双模式支持
- **间隔执行**: 按固定时间间隔重复执行（原有功能）
- **每日定时**: 每天在指定时间点执行（新增功能）

### 2. 多时间点支持
- 可设置多个每日执行时间
- 支持添加/删除时间点
- 自动按时间顺序排列

### 3. 智能调度
- 自动计算下次执行时间
- 跨天执行支持
- 实时倒计时显示

## 🎛️ 界面更新

### 新增控件

#### 定时模式选择
```
○ 间隔执行    ○ 每日定时
```
- **间隔执行**: 传统的按分钟间隔重复执行
- **每日定时**: 每天在指定时间点执行

#### 每日执行时间设置
```
每日执行时间: [09]时[00]分 [添加时间]

已设置的执行时间:
• 09:00 [删除]
• 14:00 [删除]  
• 18:00 [删除]
```

#### 状态显示增强
```
状态: 运行中
执行模式: 每日定时
下次运行: 2025-07-11 18:00:00
上次运行: 2025-07-11 14:00:00
上次导出记录数: 455
```

## 🚀 使用方法

### 设置每日定时执行

#### 步骤1: 选择定时模式
1. 在"定时模式"区域选择"每日定时"
2. 原有的"间隔时间"设置将被忽略

#### 步骤2: 添加执行时间
1. 在时间选择器中设置小时和分钟
   - 小时: 0-23 (24小时制)
   - 分钟: 0-59
2. 点击"添加时间"按钮
3. 时间会自动添加到列表中并排序

#### 步骤3: 管理时间列表
- **查看**: 所有设置的时间会显示在列表中
- **删除**: 点击时间旁边的"删除"按钮
- **排序**: 时间会自动按顺序排列

#### 步骤4: 启动定时任务
1. 确保至少设置了一个执行时间
2. 点击"开始定时任务"
3. 系统会自动计算下次执行时间

### 默认时间设置

程序启动时会自动添加三个默认执行时间：
- **09:00** - 上午9点
- **14:00** - 下午2点  
- **18:00** - 下午6点

用户可以根据需要删除或添加其他时间。

## 📊 执行逻辑

### 每日定时模式工作流程

```
1. 启动定时任务
   ↓
2. 获取当前时间
   ↓
3. 查找今天剩余的执行时间
   ↓
4. 如果有 → 等待到最近的执行时间
   如果没有 → 等待到明天第一个执行时间
   ↓
5. 到达执行时间 → 执行数据获取任务
   ↓
6. 执行完成 → 返回步骤2
```

### 时间计算示例

**当前时间**: 2025-07-11 15:30:00  
**设置时间**: 09:00, 14:00, 18:00

**计算结果**:
- 09:00 已过 ❌
- 14:00 已过 ❌  
- 18:00 未到 ✅ → 下次执行: 2025-07-11 18:00:00

**如果当前时间**: 2025-07-11 19:30:00  
**计算结果**: 今天所有时间都已过 → 下次执行: 2025-07-12 09:00:00

## 🔧 高级功能

### 1. 实时倒计时
```
距离下次执行还有 2.5 小时
距离下次执行还有 45 分钟
```

### 2. 跨天执行
- 自动处理跨天情况
- 正确计算明天的执行时间
- 支持24小时连续运行

### 3. 智能等待
- 每分钟更新一次倒计时显示
- 避免CPU资源浪费
- 响应停止命令

## 📋 使用场景

### 1. 业务时间执行
```
设置时间: 09:00, 12:00, 15:00, 18:00
用途: 在工作时间内定期获取数据
```

### 2. 高频监控
```
设置时间: 08:00, 10:00, 12:00, 14:00, 16:00, 18:00
用途: 密集监控业务数据变化
```

### 3. 特定时点
```
设置时间: 23:30
用途: 每天结束时获取全天数据
```

### 4. 多班次支持
```
设置时间: 08:00, 16:00, 00:00
用途: 支持三班倒的业务模式
```

## ⚠️ 注意事项

### 1. 时间设置
- 使用24小时制格式
- 最少需要设置一个执行时间
- 重复时间会被自动忽略

### 2. 系统时间
- 依赖系统时间的准确性
- 建议定期校准系统时间
- 时区变化可能影响执行时间

### 3. 长时间运行
- 支持7×24小时运行
- 建议定期重启程序
- 监控系统资源使用

### 4. 执行冲突
- 如果上次执行未完成，会等待完成后再开始下次执行
- 不会同时运行多个执行任务
- 执行时间可能会有轻微延迟

## 🔄 模式切换

### 从间隔执行切换到每日定时
1. 停止当前定时任务
2. 选择"每日定时"模式
3. 设置执行时间
4. 重新启动定时任务

### 从每日定时切换到间隔执行
1. 停止当前定时任务
2. 选择"间隔执行"模式
3. 设置间隔时间
4. 重新启动定时任务

## 📊 日志示例

### 启动每日定时任务
```
[2025-07-11 15:45:00] 定时任务已启动 - 每日定时 (09:00, 14:00, 18:00)
[2025-07-11 15:45:01] 距离下次执行还有 2.3 小时
```

### 执行时间到达
```
[2025-07-11 18:00:00] 开始获取数据...
[2025-07-11 18:00:01] 🚛 导出销售出库明细到旺店通出货数据文件
...
[2025-07-11 18:00:45] 🎉 数据导出完成！
[2025-07-11 18:00:46] 距离下次执行还有 15.0 小时
```

### 跨天执行
```
[2025-07-11 23:59:59] 距离下次执行还有 9.0 小时
[2025-07-12 09:00:00] 开始获取数据...
```

## 🎉 优势总结

### 1. 精确控制
- 精确到分钟的执行时间
- 符合业务时间要求
- 避免非工作时间执行

### 2. 灵活配置
- 支持多个时间点
- 动态添加/删除时间
- 实时生效无需重启

### 3. 智能调度
- 自动计算执行时间
- 跨天处理
- 资源优化

### 4. 用户友好
- 直观的时间设置界面
- 清晰的状态显示
- 详细的执行日志

---

**功能完成时间**: 2025-07-11  
**版本**: v2.0  
**新增功能**: 每日定时执行 ✅
