#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的微信发送器 - 只支持本地微信和备用方法
"""

import logging
import time
import os
from typing import List, Optional
from local_wechat_sender import LocalWeChatSender

class SimplifiedWeChatSender:
    """简化的微信发送器，支持本地微信和备用方法"""
    
    def __init__(self, contact_name: str = None):
        """
        初始化简化微信发送器
        
        Args:
            contact_name: 联系人名称
        """
        self.contact_name = contact_name
        self.logger = logging.getLogger(__name__)
        
        # 初始化本地微信发送器
        self.local_sender = None
        if contact_name:
            try:
                self.local_sender = LocalWeChatSender(contact_name)
            except Exception as e:
                self.logger.warning(f"本地微信发送器初始化失败: {e}")
    
    def test_wechat_availability(self) -> dict:
        """
        测试微信可用性
        
        Returns:
            测试结果字典
        """
        result = {
            'local_wechat': False,
            'clipboard_method': False,
            'details': {}
        }
        
        # 测试本地微信
        if self.local_sender:
            try:
                hwnd = self.local_sender.find_wechat_window()
                if hwnd:
                    result['local_wechat'] = True
                    result['details']['local_wechat'] = "微信窗口找到，可以使用本地发送"
                else:
                    result['details']['local_wechat'] = "未找到微信窗口"
            except Exception as e:
                result['details']['local_wechat'] = f"本地微信测试失败: {e}"
        
        # 测试剪贴板方法
        try:
            import pyperclip
            pyperclip.copy("test")
            test_content = pyperclip.paste()
            if test_content == "test":
                result['clipboard_method'] = True
                result['details']['clipboard_method'] = "剪贴板功能正常"
            else:
                result['details']['clipboard_method'] = "剪贴板功能异常"
        except Exception as e:
            result['details']['clipboard_method'] = f"剪贴板测试失败: {e}"
        
        return result
    
    def send_via_clipboard(self, message: str) -> bool:
        """
        通过剪贴板发送消息（备用方法）
        
        Args:
            message: 要发送的消息
            
        Returns:
            发送是否成功
        """
        try:
            import pyperclip
            
            # 将消息复制到剪贴板
            pyperclip.copy(message)
            
            self.logger.info("消息已复制到剪贴板，请手动粘贴到微信")
            self.logger.info("=" * 50)
            self.logger.info("📋 剪贴板内容:")
            self.logger.info(message)
            self.logger.info("=" * 50)
            self.logger.info("💡 请手动执行以下步骤:")
            self.logger.info("1. 打开微信")
            self.logger.info("2. 找到目标联系人或群")
            self.logger.info("3. 在输入框中按 Ctrl+V 粘贴消息")
            self.logger.info("4. 按回车发送")
            
            return True
            
        except Exception as e:
            self.logger.error(f"剪贴板发送失败: {e}")
            return False
    
    def send_via_console(self, message: str) -> bool:
        """
        通过控制台显示消息（备用方法）
        
        Args:
            message: 要发送的消息
            
        Returns:
            发送是否成功
        """
        try:
            self.logger.info("=" * 50)
            self.logger.info("🔔 京东取消订单通知:")
            self.logger.info(message)
            self.logger.info("=" * 50)
            self.logger.info("💡 请手动将以上内容发送到微信")
            
            return True
            
        except Exception as e:
            self.logger.error(f"控制台显示失败: {e}")
            return False
    
    def send_jd_cancel_orders(self, jd_logistics_numbers: List[str], contact_names: str = None) -> bool:
        """
        发送京东取消订单消息（智能选择发送方式）
        
        Args:
            jd_logistics_numbers: 京东物流单号列表
            contact_names: 联系人名称（可选，支持多个联系人，用逗号分隔）
            
        Returns:
            发送是否成功
        """
        if not jd_logistics_numbers:
            self.logger.warning("没有京东物流单号需要发送")
            return False
        
        # 构建消息内容
        content = "物流单号\n"
        for number in jd_logistics_numbers:
            content += f"{number}\n"
        
        content += f"\n以上京东单号订单取消，实物未发出共计{len(jd_logistics_numbers)}单，请处理！"
        
        target_contacts = contact_names or self.contact_name
        
        # 测试可用性
        availability = self.test_wechat_availability()
        
        # 优先级1: 本地微信发送
        if availability['local_wechat'] and self.local_sender:
            self.logger.info("🎯 使用本地微信发送...")
            try:
                if self.local_sender.send_jd_cancel_orders(jd_logistics_numbers, target_contacts):
                    self.logger.info("✅ 本地微信发送成功")
                    return True
                else:
                    self.logger.warning("⚠️ 本地微信发送失败，尝试其他方式")
            except Exception as e:
                self.logger.error(f"本地微信发送异常: {e}")
        
        # 优先级2: 剪贴板方法
        if availability['clipboard_method']:
            self.logger.info("🎯 使用剪贴板方法...")
            if self.send_via_clipboard(content):
                self.logger.info("✅ 剪贴板方法成功")
                return True
        
        # 优先级3: 控制台显示
        self.logger.info("🎯 使用控制台显示方法...")
        if self.send_via_console(content):
            self.logger.info("✅ 控制台显示方法成功")
            return True
        
        self.logger.error("❌ 所有发送方式都失败了")
        return False
    
    def send_text_message(self, message: str, contact_name: str = None) -> bool:
        """
        发送文本消息（智能选择发送方式）
        
        Args:
            message: 要发送的消息
            contact_name: 联系人名称（可选）
            
        Returns:
            发送是否成功
        """
        target_contact = contact_name or self.contact_name
        
        # 测试可用性
        availability = self.test_wechat_availability()
        
        # 优先级1: 本地微信发送
        if availability['local_wechat'] and self.local_sender:
            try:
                if self.local_sender.send_text_message(message, target_contact):
                    return True
            except Exception as e:
                self.logger.error(f"本地微信发送异常: {e}")
        
        # 优先级2: 剪贴板方法
        if availability['clipboard_method']:
            return self.send_via_clipboard(message)
        
        # 优先级3: 控制台显示
        return self.send_via_console(message)

if __name__ == '__main__':
    # 测试简化微信发送器
    sender = SimplifiedWeChatSender(contact_name="文件传输助手")
    
    # 测试可用性
    availability = sender.test_wechat_availability()
    print("微信发送可用性测试:")
    for method, available in availability.items():
        if method != 'details':
            status = "✅ 可用" if available else "❌ 不可用"
            print(f"  {method}: {status}")
    
    print("\n详细信息:")
    for method, detail in availability['details'].items():
        print(f"  {method}: {detail}")
    
    # 测试发送
    test_message = f"简化微信发送器测试 - {time.strftime('%Y-%m-%d %H:%M:%S')}"
    print(f"\n测试发送消息: {test_message}")
    
    if sender.send_text_message(test_message):
        print("✅ 消息发送成功")
    else:
        print("❌ 消息发送失败")
