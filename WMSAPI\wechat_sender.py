#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
微信消息发送模块
支持通过企业微信机器人发送消息
"""

import requests
import json
import logging
from typing import List, Optional
from datetime import datetime

class WeChatSender:
    """微信消息发送器"""
    
    def __init__(self, webhook_url: str = ""):
        """
        初始化微信发送器
        
        Args:
            webhook_url: 企业微信机器人webhook地址
        """
        self.webhook_url = webhook_url
        self.logger = logging.getLogger(__name__)
        
    def send_text_message(self, content: str, mentioned_list: Optional[List[str]] = None) -> bool:
        """
        发送文本消息
        
        Args:
            content: 消息内容
            mentioned_list: @的用户列表（手机号或@all）
            
        Returns:
            发送是否成功
        """
        if not self.webhook_url:
            self.logger.error("未配置微信机器人webhook地址")
            return False
            
        try:
            # 构建消息体
            message = {
                "msgtype": "text",
                "text": {
                    "content": content
                }
            }
            
            # 添加@用户
            if mentioned_list:
                message["text"]["mentioned_list"] = mentioned_list
            
            # 发送请求
            response = requests.post(
                self.webhook_url,
                json=message,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    self.logger.info("微信消息发送成功")
                    return True
                else:
                    self.logger.error(f"微信消息发送失败: {result.get('errmsg', '未知错误')}")
                    return False
            else:
                self.logger.error(f"HTTP请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"发送微信消息异常: {e}")
            return False
    
    def send_jd_cancel_orders(self, jd_logistics_numbers: List[str], contact_name: str = "") -> bool:
        """
        发送京东取消订单消息
        
        Args:
            jd_logistics_numbers: 京东物流单号列表
            contact_name: 联系人名称（用于@）
            
        Returns:
            发送是否成功
        """
        if not jd_logistics_numbers:
            self.logger.warning("没有京东物流单号需要发送")
            return False
        
        # 构建消息内容
        content = "物流单号\n"
        for number in jd_logistics_numbers:
            content += f"{number}\n"
        
        content += f"\n以上京东单号订单取消，实物未发出共计{len(jd_logistics_numbers)}单，请处理！"
        
        # 构建@用户列表
        mentioned_list = []
        if contact_name:
            mentioned_list.append(contact_name)
        
        return self.send_text_message(content, mentioned_list)
    
    def test_connection(self) -> bool:
        """
        测试微信机器人连接
        
        Returns:
            连接是否正常
        """
        if not self.webhook_url:
            return False
            
        test_message = f"微信机器人连接测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        return self.send_text_message(test_message)

class WeChatConfig:
    """微信配置管理"""
    
    @staticmethod
    def get_webhook_url_from_key(key: str) -> str:
        """
        根据机器人key生成webhook URL
        
        Args:
            key: 企业微信机器人的key
            
        Returns:
            完整的webhook URL
        """
        if not key:
            return ""
        
        # 如果已经是完整URL，直接返回
        if key.startswith("https://"):
            return key
        
        # 否则构建完整URL
        return f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={key}"
    
    @staticmethod
    def validate_webhook_url(url: str) -> bool:
        """
        验证webhook URL格式
        
        Args:
            url: webhook URL
            
        Returns:
            URL是否有效
        """
        if not url:
            return False
        
        # 检查是否是企业微信机器人URL
        return url.startswith("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=")

def extract_jd_logistics_numbers(trades: List[dict]) -> List[str]:
    """
    从订单列表中提取京东物流单号
    
    Args:
        trades: 订单列表
        
    Returns:
        京东物流单号列表
    """
    jd_numbers = []
    
    for trade in trades:
        # 尝试从不同字段获取物流单号
        logistics_fields = ['logistics_no', 'express_no', 'tracking_no', 'waybill_no']
        
        for field in logistics_fields:
            logistics_no = trade.get(field, '')
            if logistics_no and str(logistics_no).upper().startswith('JD'):
                jd_numbers.append(str(logistics_no))
                break
    
    # 去重并排序
    return sorted(list(set(jd_numbers)))

def format_jd_cancel_message(jd_numbers: List[str]) -> str:
    """
    格式化京东取消订单消息
    
    Args:
        jd_numbers: 京东物流单号列表
        
    Returns:
        格式化的消息内容
    """
    if not jd_numbers:
        return ""
    
    content = "物流单号\n"
    for number in jd_numbers:
        content += f"{number}\n"
    
    content += f"\n以上京东单号订单取消，实物未发出共计{len(jd_numbers)}单，请处理！"
    
    return content

# 测试函数
def test_wechat_sender():
    """测试微信发送功能"""
    # 示例配置
    webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY_HERE"
    
    sender = WeChatSender(webhook_url)
    
    # 测试京东物流单号发送
    test_jd_numbers = [
        "JDAZ20474031170",
        "JDAZ20473059809", 
        "JDAZ20472646572"
    ]
    
    success = sender.send_jd_cancel_orders(test_jd_numbers, "13800138000")
    print(f"发送结果: {'成功' if success else '失败'}")

if __name__ == "__main__":
    test_wechat_sender()
