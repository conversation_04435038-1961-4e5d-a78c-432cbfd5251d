#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
本地微信消息发送模块
支持直接操作本地微信窗口发送消息
"""

import time
import logging
import pyautogui
import pyperclip
from typing import List, Optional
import win32gui
import win32con
import win32clipboard
from datetime import datetime

class LocalWeChatSender:
    """本地微信消息发送器"""
    
    def __init__(self, contact_name: str = ""):
        """
        初始化本地微信发送器
        
        Args:
            contact_name: 微信联系人名称或群名称
        """
        self.contact_name = contact_name
        self.logger = logging.getLogger(__name__)
        
        # 设置pyautogui参数
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
    def find_wechat_window(self) -> Optional[int]:
        """
        查找微信窗口
        
        Returns:
            微信窗口句柄，如果未找到返回None
        """
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if "微信" in window_text or "WeChat" in window_text:
                    windows.append((hwnd, window_text))
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            # 返回第一个找到的微信窗口
            hwnd, title = windows[0]
            self.logger.info(f"找到微信窗口: {title}")
            return hwnd
        else:
            self.logger.error("未找到微信窗口，请确保微信已打开")
            return None
    
    def activate_wechat_window(self, hwnd: int) -> bool:
        """
        激活微信窗口

        Args:
            hwnd: 微信窗口句柄

        Returns:
            是否成功激活
        """
        try:
            # 检查窗口是否最小化
            if win32gui.IsIconic(hwnd):
                self.logger.debug("微信窗口已最小化，正在恢复...")
                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                time.sleep(1.5)  # 给更多时间恢复
            else:
                # 显示窗口
                win32gui.ShowWindow(hwnd, win32con.SW_SHOW)
                time.sleep(0.5)

            # 确保窗口正常显示
            win32gui.ShowWindow(hwnd, win32con.SW_NORMAL)
            time.sleep(0.5)

            # 设置为前台窗口
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(1)

            # 验证窗口是否成功激活
            try:
                current_hwnd = win32gui.GetForegroundWindow()
                if current_hwnd == hwnd:
                    self.logger.debug("✅ 微信窗口成功激活")
                else:
                    self.logger.debug(f"⚠️ 微信窗口激活状态未确认，当前前台窗口: {current_hwnd}")
            except:
                pass  # 忽略验证错误

            return True

        except Exception as e:
            self.logger.error(f"激活微信窗口失败: {e}")
            return False
    
    def search_contact(self, contact_name: str) -> bool:
        """
        搜索并打开联系人对话框
        
        Args:
            contact_name: 联系人名称
            
        Returns:
            是否成功找到联系人
        """
        try:
            # 使用Ctrl+F打开搜索框
            pyautogui.hotkey('ctrl', 'f')
            time.sleep(1)
            
            # 输入联系人名称
            pyautogui.typewrite(contact_name, interval=0.1)
            time.sleep(1)
            
            # 按回车选择第一个搜索结果
            pyautogui.press('enter')
            time.sleep(1)
            
            # 再按一次回车进入对话
            pyautogui.press('enter')
            time.sleep(1)
            
            self.logger.info(f"成功打开与 {contact_name} 的对话")
            return True
            
        except Exception as e:
            self.logger.error(f"搜索联系人失败: {e}")
            return False
    
    def send_message_to_clipboard(self, message: str) -> bool:
        """
        将消息复制到剪贴板并发送
        
        Args:
            message: 要发送的消息
            
        Returns:
            是否成功发送
        """
        try:
            # 将消息复制到剪贴板
            pyperclip.copy(message)
            time.sleep(0.5)
            
            # 点击输入框（假设输入框在窗口下方）
            # 这里可能需要根据实际微信界面调整坐标
            pyautogui.click(pyautogui.size()[0] // 2, pyautogui.size()[1] - 100)
            time.sleep(0.5)
            
            # 粘贴消息
            pyautogui.hotkey('ctrl', 'v')
            time.sleep(0.5)
            
            # 发送消息
            pyautogui.press('enter')
            time.sleep(1)
            
            self.logger.info("消息发送成功")
            return True
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    def send_text_message(self, message: str, contact_name: str = None) -> bool:
        """
        发送文本消息
        
        Args:
            message: 消息内容
            contact_name: 联系人名称（可选，使用初始化时的联系人）
            
        Returns:
            发送是否成功
        """
        target_contact = contact_name or self.contact_name
        
        if not target_contact:
            self.logger.error("未指定联系人名称")
            return False
        
        try:
            # 1. 查找微信窗口
            hwnd = self.find_wechat_window()
            if not hwnd:
                return False
            
            # 2. 激活微信窗口
            if not self.activate_wechat_window(hwnd):
                return False
            
            # 3. 搜索并打开联系人对话
            if not self.search_contact(target_contact):
                return False
            
            # 4. 发送消息
            return self.send_message_to_clipboard(message)
            
        except Exception as e:
            self.logger.error(f"发送微信消息异常: {e}")
            return False
    
    def send_jd_cancel_orders(self, jd_logistics_numbers: List[str], contact_names: str = None) -> bool:
        """
        发送京东取消订单消息

        Args:
            jd_logistics_numbers: 京东物流单号列表
            contact_names: 联系人名称（可选，支持多个联系人，用逗号分隔）

        Returns:
            发送是否成功
        """
        if not jd_logistics_numbers:
            self.logger.warning("没有京东物流单号需要发送")
            return False

        # 构建消息内容
        content = "物流单号\n"
        for number in jd_logistics_numbers:
            content += f"{number}\n"

        content += f"\n以上京东单号订单取消，实物未发出共计{len(jd_logistics_numbers)}单，请处理！"

        # 支持多个联系人
        if contact_names:
            contacts = [name.strip() for name in contact_names.split(',') if name.strip()]
            success_count = 0

            for contact in contacts:
                if self.send_text_message(content, contact):
                    success_count += 1
                    self.logger.info(f"成功发送给联系人: {contact}")
                else:
                    self.logger.error(f"发送失败给联系人: {contact}")

            return success_count > 0
        else:
            return self.send_text_message(content, self.contact_name)
    
    def test_connection(self, contact_name: str = None) -> bool:
        """
        测试微信连接
        
        Args:
            contact_name: 测试联系人名称
            
        Returns:
            连接是否正常
        """
        target_contact = contact_name or self.contact_name
        
        if not target_contact:
            self.logger.error("未指定测试联系人名称")
            return False
        
        test_message = f"微信连接测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        return self.send_text_message(test_message, target_contact)

class WeChatWindowFinder:
    """微信窗口查找器"""
    
    @staticmethod
    def get_all_wechat_windows():
        """获取所有微信窗口信息"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)
                if "微信" in window_text or "WeChat" in window_text or "WeChatMainWndForPC" in class_name:
                    rect = win32gui.GetWindowRect(hwnd)
                    windows.append({
                        'hwnd': hwnd,
                        'title': window_text,
                        'class_name': class_name,
                        'rect': rect
                    })
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        return windows
    
    @staticmethod
    def print_wechat_windows():
        """打印所有微信窗口信息"""
        windows = WeChatWindowFinder.get_all_wechat_windows()
        
        if windows:
            print("找到的微信窗口:")
            for i, window in enumerate(windows):
                print(f"{i+1}. 标题: {window['title']}")
                print(f"   类名: {window['class_name']}")
                print(f"   句柄: {window['hwnd']}")
                print(f"   位置: {window['rect']}")
                print()
        else:
            print("未找到微信窗口")

def test_local_wechat_sender():
    """测试本地微信发送功能"""
    # 打印微信窗口信息
    print("=== 微信窗口信息 ===")
    WeChatWindowFinder.print_wechat_windows()
    
    # 测试发送消息
    contact_name = input("请输入要发送消息的联系人名称: ").strip()
    
    if contact_name:
        sender = LocalWeChatSender(contact_name)
        
        # 测试连接
        print("正在测试微信连接...")
        if sender.test_connection():
            print("✅ 微信连接测试成功")
            
            # 测试京东物流单号发送
            test_jd_numbers = [
                "JDAZ20474031170",
                "JDAZ20473059809", 
                "JDAZ20472646572"
            ]
            
            print("正在发送测试京东物流单号...")
            success = sender.send_jd_cancel_orders(test_jd_numbers)
            print(f"发送结果: {'成功' if success else '失败'}")
        else:
            print("❌ 微信连接测试失败")
    else:
        print("未输入联系人名称，跳过测试")

if __name__ == "__main__":
    test_local_wechat_sender()
