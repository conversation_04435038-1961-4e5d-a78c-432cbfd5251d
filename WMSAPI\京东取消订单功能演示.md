# 京东取消订单微信通知功能演示

## 🎯 功能概述

本功能专门用于自动获取已取消销售订单中的京东物流单号，并通过企业微信机器人发送通知。

## 📱 微信通知效果

### 通知消息格式
```
物流单号
JDAZ20474031170
JDAZ20473059809
JDAZ20472646572
JDAZ20471823704
JDAZ20471048319
JDAZ20470824254
JDAZ20470679228
JDAZ20470581353
JDAZ20470580120
JDAZ20470684906
JDAZ20470131264
JDAZ20468526480
JDAZ20467261890

以上京东单号订单取消，实物未发出共计13单，请处理！
```

### 通知特点
- ✅ 自动提取JD开头的物流单号
- ✅ 按照用户要求的格式发送
- ✅ 显示总数量统计
- ✅ 支持@指定联系人
- ✅ 只发送已取消订单的京东物流单号

## 🖥️ 界面功能

### 主界面布局
```
┌─────────────────────────────────────────┐
│           京东取消订单微信通知工具           │
├─────────────────────────────────────────┤
│ 📱 微信机器人配置                        │
│   机器人Webhook URL: [_______________]   │
│   @联系人: [_______] [测试微信连接]      │
├─────────────────────────────────────────┤
│ ⏰ 定时设置                             │
│   ○ 间隔执行  ○ 每日定时                │
│   间隔时间: [60] 分钟                   │
│   每日执行时间: [09]时[00]分 [添加时间]   │
│   已设置: 09:00 [删除] 14:00 [删除]      │
│   ☑ 程序启动时自动开始定时任务            │
├─────────────────────────────────────────┤
│ 🎮 任务控制                             │
│   [开始定时任务] [停止定时任务] [立即执行] │
├─────────────────────────────────────────┤
│ 📊 运行状态                             │
│   状态: 运行中                          │
│   执行模式: 每日定时                     │
│   下次运行: 2025-07-16 14:00:00         │
│   上次运行: 2025-07-16 09:00:15         │
│   上次结果: 取消订单5个，京东3个，通知已发送 │
├─────────────────────────────────────────┤
│ 📝 运行日志                             │
│   [09:00:15] 开始处理京东取消订单...      │
│   [09:00:16] 查询到5个已取消出库单       │
│   [09:00:16] 提取到3个京东物流单号       │
│   [09:00:16] 京东物流单号:              │
│   [09:00:16]   JDAZ20474031170         │
│   [09:00:16]   JDAZ20473059809         │
│   [09:00:16]   JDAZ20472646572         │
│   [09:00:17] ✅ 微信通知发送成功          │
│   [09:00:17] ✅ 处理完成                │
└─────────────────────────────────────────┘
```

## 🔄 工作流程

### 1. 数据获取流程
```
开始
  ↓
查询当天已取消出库单
  ↓
筛选状态为"5"(已取消)的记录
  ↓
提取物流单号字段
  ↓
筛选JD开头的物流单号
  ↓
去重并排序
  ↓
生成通知消息
  ↓
发送微信通知
  ↓
记录执行结果
  ↓
结束
```

### 2. 定时执行流程

#### 每日定时模式
```
程序启动
  ↓
加载配置的执行时间
  ↓
计算下次执行时间
  ↓
等待到执行时间
  ↓
执行京东取消订单处理
  ↓
更新状态显示
  ↓
计算下次执行时间
  ↓
继续等待...
```

#### 间隔执行模式
```
程序启动
  ↓
设置间隔时间
  ↓
执行京东取消订单处理
  ↓
等待设定的间隔时间
  ↓
再次执行处理
  ↓
循环继续...
```

## 📋 配置示例

### 微信机器人配置
```json
{
  "wechat_webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxxxxxxx",
  "wechat_contact": "13800138000",
  "timer_mode": "daily",
  "daily_times": ["09:00", "14:00", "18:00"],
  "auto_start": true
}
```

### 执行时间设置
- **上午检查**: 09:00 - 处理夜间产生的取消订单
- **下午检查**: 14:00 - 处理上午的取消订单  
- **晚上检查**: 18:00 - 处理下午的取消订单

## 🎯 使用场景

### 场景1: 电商客服团队
- **需求**: 及时了解京东订单取消情况
- **配置**: 每日3次定时检查(09:00, 14:00, 18:00)
- **通知**: @客服主管处理

### 场景2: 仓库管理
- **需求**: 实时监控取消订单，避免发货
- **配置**: 每30分钟检查一次
- **通知**: @仓库管理员

### 场景3: 财务对账
- **需求**: 每日汇总取消订单数据
- **配置**: 每日晚上20:00统一检查
- **通知**: @财务人员

## 📊 数据统计

### 执行结果统计
- **总取消订单数**: 当天查询到的所有已取消出库单数量
- **京东物流单号数**: 从中提取到的京东物流单号数量
- **通知发送状态**: 微信通知是否发送成功
- **处理时间**: 每次执行的具体时间

### 日志记录
- 详细的执行过程记录
- 错误信息和异常处理
- API调用结果
- 微信发送状态

## 🔧 技术特点

### API集成
- 使用旺店通出库单查询API
- 自动处理分页查询
- 支持大量数据处理

### 微信集成
- 企业微信机器人API
- 支持@指定用户
- 消息格式化处理

### 界面设计
- 直观的可视化界面
- 实时状态显示
- 详细的日志输出

### 配置管理
- 自动保存配置
- 启动时自动加载
- 支持配置导入导出

## 🚀 快速体验

1. **启动程序**
   ```bash
   python jd_cancel_gui.py
   ```

2. **配置微信机器人**
   - 填写webhook地址
   - 设置联系人
   - 测试连接

3. **设置定时任务**
   - 选择每日定时模式
   - 添加执行时间: 09:00
   - 启用自动启动

4. **开始使用**
   - 点击"开始定时任务"
   - 或点击"立即执行一次"测试

## 💡 最佳实践

1. **合理设置执行频率**
   - 避免过于频繁的查询
   - 根据业务需求选择合适的时间点

2. **监控执行状态**
   - 定期查看日志输出
   - 关注错误信息和异常

3. **测试微信通知**
   - 定期测试微信连接
   - 确保通知能正常发送

4. **备份配置**
   - 保存重要的配置信息
   - 定期备份配置文件
