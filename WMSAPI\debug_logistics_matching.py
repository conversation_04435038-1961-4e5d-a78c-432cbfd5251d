#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试物流单号匹配问题
"""

import logging
from datetime import datetime, timedelta
from stockout_details_manager import StockoutDetailsManager
import json

def setup_logging():
    """配置日志记录"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def debug_logistics_matching():
    """调试物流单号匹配问题"""
    logger = setup_logging()
    manager = StockoutDetailsManager()
    
    logger.info("=== 调试物流单号匹配问题 ===")
    
    # 1. 获取已取消的销售订单
    logger.info("1. 获取已取消的销售订单...")
    canceled_orders = manager.get_canceled_sales_orders_today()
    
    logger.info(f"找到 {len(canceled_orders)} 个已取消订单:")
    for i, order in enumerate(canceled_orders):
        logger.info(f"  订单 {i+1}:")
        logger.info(f"    订单号: {order.get('trade_no', '未知')}")
        logger.info(f"    原始订单号: {order.get('src_tids', '未知')}")
        logger.info(f"    物流编码: {order.get('logistics_code', '未知')}")
        logger.info(f"    物流单号: {order.get('logistics_no', '未知')}")
        logger.info(f"    平台: {order.get('platform_name', '未知')}")
        logger.info(f"    状态: {order.get('trade_status', '未知')}")
    
    # 2. 获取所有出库单
    logger.info("\n2. 获取所有出库单...")
    all_stockouts = manager.get_all_stockouts_today()
    
    logger.info(f"找到 {len(all_stockouts)} 个出库单")
    
    # 分析出库单中的京东物流
    jd_stockouts = []
    for stockout in all_stockouts:
        logistics_code = stockout.get('logistics_code', '')
        if logistics_code and logistics_code.upper() in ['JBD', 'JD', 'JINGDONG']:
            jd_stockouts.append(stockout)
    
    logger.info(f"其中 {len(jd_stockouts)} 个是京东物流出库单:")
    for i, stockout in enumerate(jd_stockouts):
        logger.info(f"  京东出库单 {i+1}:")
        logger.info(f"    出库单号: {stockout.get('stockout_no', '未知')}")
        logger.info(f"    订单号: {stockout.get('src_oms_no', '未知')}")
        logger.info(f"    原始订单号: {stockout.get('src_tids', '未知')}")
        logger.info(f"    物流单号: {stockout.get('logistics_no', '未知')}")
        logger.info(f"    物流编码: {stockout.get('logistics_code', '未知')}")
        logger.info(f"    状态: {stockout.get('status', '未知')}")
    
    # 3. 分析匹配问题
    logger.info("\n3. 分析匹配问题...")
    
    # 提取已取消订单的订单号
    canceled_trade_nos = set()
    canceled_src_tids = set()
    
    for order in canceled_orders:
        trade_no = order.get('trade_no', '')
        src_tids = order.get('src_tids', '')
        if trade_no:
            canceled_trade_nos.add(trade_no)
        if src_tids:
            canceled_src_tids.add(src_tids)
    
    logger.info(f"已取消订单号集合: {canceled_trade_nos}")
    logger.info(f"已取消原始订单号集合: {canceled_src_tids}")
    
    # 检查出库单匹配
    matched_stockouts = []
    unmatched_stockouts = []
    
    for stockout in all_stockouts:
        src_oms_no = stockout.get('src_oms_no', '')
        src_tids = stockout.get('src_tids', '')
        
        is_matched = (src_oms_no in canceled_trade_nos or 
                     src_tids in canceled_src_tids)
        
        if is_matched:
            matched_stockouts.append(stockout)
        else:
            unmatched_stockouts.append(stockout)
    
    logger.info(f"\n匹配结果:")
    logger.info(f"  匹配的出库单: {len(matched_stockouts)} 个")
    logger.info(f"  未匹配的出库单: {len(unmatched_stockouts)} 个")
    
    # 显示匹配的出库单详情
    if matched_stockouts:
        logger.info(f"\n匹配的出库单详情:")
        for i, stockout in enumerate(matched_stockouts):
            logger.info(f"  匹配出库单 {i+1}:")
            logger.info(f"    出库单号: {stockout.get('stockout_no', '未知')}")
            logger.info(f"    订单号: {stockout.get('src_oms_no', '未知')}")
            logger.info(f"    原始订单号: {stockout.get('src_tids', '未知')}")
            logger.info(f"    物流单号: {stockout.get('logistics_no', '未知')}")
            logger.info(f"    物流编码: {stockout.get('logistics_code', '未知')}")
            logger.info(f"    状态: {stockout.get('status', '未知')}")
    
    # 4. 分析字段匹配问题
    logger.info("\n4. 分析字段匹配问题...")
    
    # 检查出库单中的所有订单号字段
    all_order_fields = set()
    for stockout in all_stockouts[:10]:  # 只检查前10个
        for key, value in stockout.items():
            if 'order' in key.lower() or 'trade' in key.lower() or 'tid' in key.lower():
                all_order_fields.add(key)
    
    logger.info(f"出库单中包含订单相关的字段: {sorted(all_order_fields)}")
    
    # 显示前几个出库单的所有字段
    logger.info(f"\n前3个出库单的详细字段:")
    for i, stockout in enumerate(all_stockouts[:3]):
        logger.info(f"  出库单 {i+1} 的所有字段:")
        for key, value in stockout.items():
            if value:  # 只显示有值的字段
                logger.info(f"    {key}: {value}")
    
    # 5. 尝试不同的匹配策略
    logger.info("\n5. 尝试不同的匹配策略...")
    
    # 策略1: 使用trade_no字段匹配
    strategy1_matches = []
    for stockout in all_stockouts:
        trade_no = stockout.get('trade_no', '')
        if trade_no in canceled_trade_nos:
            strategy1_matches.append(stockout)
    
    logger.info(f"策略1 (trade_no字段匹配): {len(strategy1_matches)} 个匹配")
    
    # 策略2: 使用所有可能的订单号字段
    strategy2_matches = []
    order_fields = ['trade_no', 'src_oms_no', 'src_tids', 'order_no', 'tid']
    
    for stockout in all_stockouts:
        matched = False
        for field in order_fields:
            field_value = stockout.get(field, '')
            if field_value and (field_value in canceled_trade_nos or field_value in canceled_src_tids):
                strategy2_matches.append(stockout)
                matched = True
                break
    
    logger.info(f"策略2 (多字段匹配): {len(strategy2_matches)} 个匹配")
    
    # 6. 检查时间范围问题
    logger.info("\n6. 检查时间范围问题...")
    
    # 扩大时间范围查询
    now = datetime.now()
    extended_start = (now - timedelta(days=2)).strftime('%Y-%m-%d %H:%M:%S')
    extended_end = now.strftime('%Y-%m-%d %H:%M:%S')
    
    logger.info(f"扩大时间范围查询: {extended_start} 至 {extended_end}")
    
    extended_stockouts = manager.get_all_stockouts(extended_start, extended_end)
    logger.info(f"扩大时间范围后找到 {len(extended_stockouts)} 个出库单")
    
    # 在扩大范围内重新匹配
    extended_matches = []
    for stockout in extended_stockouts:
        src_oms_no = stockout.get('src_oms_no', '')
        src_tids = stockout.get('src_tids', '')
        
        is_matched = (src_oms_no in canceled_trade_nos or 
                     src_tids in canceled_src_tids)
        
        if is_matched:
            extended_matches.append(stockout)
    
    logger.info(f"扩大时间范围后匹配: {len(extended_matches)} 个出库单")
    
    if extended_matches:
        logger.info(f"扩大范围匹配的出库单:")
        for i, stockout in enumerate(extended_matches):
            logger.info(f"  匹配出库单 {i+1}:")
            logger.info(f"    出库单号: {stockout.get('stockout_no', '未知')}")
            logger.info(f"    订单号: {stockout.get('src_oms_no', '未知')}")
            logger.info(f"    物流单号: {stockout.get('logistics_no', '未知')}")
            logger.info(f"    物流编码: {stockout.get('logistics_code', '未知')}")
            logger.info(f"    创建时间: {stockout.get('created', '未知')}")
            logger.info(f"    发货时间: {stockout.get('consign_time', '未知')}")

if __name__ == '__main__':
    print("🔍 开始调试物流单号匹配问题...")
    debug_logistics_matching()
    print("\n🎯 调试完成")
